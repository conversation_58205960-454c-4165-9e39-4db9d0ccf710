{
    "stake_currency": "USDT",
    "fiat_display_currency": "USD",
    "dry_run": true,
    "dry_run_wallet": 10000, /* 模拟交易钱包初始金额 */
    "available_capital": 10000,
    "tradable_balance_ratio": 0.99,
    "max_open_trades": 50,
    "stake_amount": "unlimited",
    "amend_last_stake_amount": false,
    "last_stake_amount_min_ratio": 0.5,
    "position_adjustment_enable": true,
    "process_only_new_candles": true,
    "cancel_open_orders_on_exit": false,
    "ignore_roi_if_entry_signal": true,
    "trading_mode": "futures", /* 交易模式：期货 */
    "margin_mode": "cross", /* 保证金模式：逐仓是isolated，全仓是cross */
    "futures_funding_rate": true,
    "validate_leverage_tiers": false,
    "timezone": "Asia/Shanghai",
    "user_data_dir": "/freqtrade/user_data",
    "db_url": "sqlite:////freqtrade/user_data/tradesv3.sqlite",
    "log": {
        "level": "DEBUG",
        "file": "/freqtrade/user_data/logs/freqtrade.log",
        "rotation": {
            "max_size": "10MB",
            "retention": "7 days"
        }
    },
    "unfilledtimeout": {
        "entry": 10,
        "exit": 10,
        "exit_timeout_count": 0,
        "unit": "minutes"
    },
    "order_types": {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": true,
        "stoploss_on_exchange_interval": 60
    },
    "entry_pricing": {
        "price_side": "other", /* 入场价格方向，other表示使用对手方价格 */
        "use_order_book": true, /* 是否使用订单簿 */
        "order_book_top": 1, /* 使用订单簿的深度 */
        "price_last_balance": 0.0,
        "check_depth_of_market": {
            "enabled": false
        }
    },
    "exit_pricing": {
        "price_side": "other",
        "use_order_book": true,
        "order_book_top": 1
    },
    "exchange": {
        "name": "binance",
        "key": "",
        "secret": "",
        "ccxt_async_config": {
            "enableRateLimit": true,
            "defaultType": "future",
            "rateLimit": 50
        },
        "ccxt_config": {
            "enableRateLimit": true,
            "defaultType": "future",
            "rateLimit": 50
        },
        "exchange_info_pairs_refresh": true,
        "pair_whitelist": [
            "1000000MOG/USDT:USDT",
            "1000BONK/USDT:USDT",
            "1000CAT/USDT:USDT",
            "1000CHEEMS/USDT:USDT",
            "1000FLOKI/USDT:USDT",
            "1000LUNC/USDT:USDT",
            "1000PEPE/USDT:USDT",
            "1000RATS/USDT:USDT",
            "1000SATS/USDT:USDT",
            "1000SHIB/USDT:USDT",
            "1000WHY/USDT:USDT",
            "1000X/USDT:USDT",
            "1000XEC/USDT:USDT",
            "1INCH/USDT:USDT",
            "1MBABYDOGE/USDT:USDT",
            "AAVE/USDT:USDT",
            "ACE/USDT:USDT",
            "ACH/USDT:USDT",
            "ACT/USDT:USDT",
            "ACX/USDT:USDT",
            "ADA/USDT:USDT",
            "AERO/USDT:USDT",
            "AEVO/USDT:USDT",
            "AGLD/USDT:USDT",
            "AI/USDT:USDT",
            "AI16Z/USDT:USDT",
            "AIXBT/USDT:USDT",
            "AKT/USDT:USDT",
            "ALCH/USDT:USDT",
            "ALGO/USDT:USDT",
            "ALICE/USDT:USDT",
            "ALPACA/USDT:USDT",
            "ALPHA/USDT:USDT",
            "ALT/USDT:USDT",
            "ANIME/USDT:USDT",
            "ANKR/USDT:USDT",
            "APE/USDT:USDT",
            "API3/USDT:USDT",
            "APT/USDT:USDT",
            "AR/USDT:USDT",
            "ARB/USDT:USDT",
            "ARC/USDT:USDT",
            "ARK/USDT:USDT",
            "ARKM/USDT:USDT",
            "ARPA/USDT:USDT",
            "ASTR/USDT:USDT",
            "ATA/USDT:USDT",
            "ATH/USDT:USDT",
            "ATOM/USDT:USDT",
            "AUCTION/USDT:USDT",
            "AVA/USDT:USDT",
            "AVAAI/USDT:USDT",
            "AVAX/USDT:USDT",
            "AXL/USDT:USDT",
            "AXS/USDT:USDT",
            "B3/USDT:USDT",
            "BABY/USDT:USDT",
            "BADGER/USDT:USDT",
            "BAKE/USDT:USDT",
            "BAL/USDT:USDT",
            "BAN/USDT:USDT",
            "BANANA/USDT:USDT",
            "BANANAS31/USDT:USDT",
            "BAND/USDT:USDT",
            "BAT/USDT:USDT",
            "BB/USDT:USDT",
            "BCH/USDT:USDT",
            "BEAMX/USDT:USDT",
            "BEL/USDT:USDT",
            "BERA/USDT:USDT",
            "BICO/USDT:USDT",
            "BID/USDT:USDT",
            "BIGTIME/USDT:USDT",
            "BIO/USDT:USDT",
            "BLUR/USDT:USDT",
            "BMT/USDT:USDT",
            "BNB/USDT:USDT",
            "BNT/USDT:USDT",
            "BOME/USDT:USDT",
            "BR/USDT:USDT",
            "BRETT/USDT:USDT",
            "BROCCOLI714/USDT:USDT",
            "BROCCOLIF3B/USDT:USDT",
            "BSV/USDT:USDT",
            "BSW/USDT:USDT",
            "BTC/USDT:USDT",
            "C98/USDT:USDT",
            "CAKE/USDT:USDT",
            "CATI/USDT:USDT",
            "CELO/USDT:USDT",
            "CELR/USDT:USDT",
            "CETUS/USDT:USDT",
            "CFX/USDT:USDT",
            "CGPT/USDT:USDT",
            "CHESS/USDT:USDT",
            "CHILLGUY/USDT:USDT",
            "CHR/USDT:USDT",
            "CHZ/USDT:USDT",
            "CKB/USDT:USDT",
            "COMP/USDT:USDT",
            "COOKIE/USDT:USDT",
            "COS/USDT:USDT",
            "COTI/USDT:USDT",
            "COW/USDT:USDT",
            "CRV/USDT:USDT",
            "CTSI/USDT:USDT",
            "CYBER/USDT:USDT",
            "D/USDT:USDT",
            "DASH/USDT:USDT",
            "DEFI/USDT:USDT",
            "DEGEN/USDT:USDT",
            "DEGO/USDT:USDT",
            "DENT/USDT:USDT",
            "DEXE/USDT:USDT",
            "DF/USDT:USDT",
            "DIA/USDT:USDT",
            "DODOX/USDT:USDT",
            "DOGE/USDT:USDT",
            "DOGS/USDT:USDT",
            "DOT/USDT:USDT",
            "DRIFT/USDT:USDT",
            "DUSK/USDT:USDT",
            "DYDX/USDT:USDT",
            "DYM/USDT:USDT",
            "EDU/USDT:USDT",
            "EGLD/USDT:USDT",
            "EIGEN/USDT:USDT",
            "ENA/USDT:USDT",
            "ENJ/USDT:USDT",
            "ENS/USDT:USDT",
            "EOS/USDT:USDT",
            "EPIC/USDT:USDT",
            "ETC/USDT:USDT",
            "ETH/USDT:USDT",
            "ETHFI/USDT:USDT",
            "ETHW/USDT:USDT",
            "FARTCOIN/USDT:USDT",
            "FET/USDT:USDT",
            "FIDA/USDT:USDT",
            "FIL/USDT:USDT",
            "FIO/USDT:USDT",
            "FLM/USDT:USDT",
            "FLOW/USDT:USDT",
            "FLUX/USDT:USDT",
            "FORM/USDT:USDT",
            "FUN/USDT:USDT",
            "FXS/USDT:USDT",
            "G/USDT:USDT",
            "GALA/USDT:USDT",
            "GAS/USDT:USDT",
            "GHST/USDT:USDT",
            "GLM/USDT:USDT",
            "GMT/USDT:USDT",
            "GMX/USDT:USDT",
            "GOAT/USDT:USDT",
            "GPS/USDT:USDT",
            "GRASS/USDT:USDT",
            "GRIFFAIN/USDT:USDT",
            "GRT/USDT:USDT",
            "GTC/USDT:USDT",
            "GUN/USDT:USDT",
            "HBAR/USDT:USDT",
            "HEI/USDT:USDT",
            "HFT/USDT:USDT",
            "HIFI/USDT:USDT",
            "HIGH/USDT:USDT",
            "HIPPO/USDT:USDT",
            "HIVE/USDT:USDT",
            "HMSTR/USDT:USDT",
            "HOOK/USDT:USDT",
            "HOT/USDT:USDT",
            "ICP/USDT:USDT",
            "ICX/USDT:USDT",
            "ID/USDT:USDT",
            "ILV/USDT:USDT",
            "IMX/USDT:USDT",
            "INJ/USDT:USDT",
            "IO/USDT:USDT",
            "IOST/USDT:USDT",
            "IOTA/USDT:USDT",
            "IOTX/USDT:USDT",
            "IP/USDT:USDT",
            "JASMY/USDT:USDT",
            "JELLYJELLY/USDT:USDT",
            "JOE/USDT:USDT",
            "JTO/USDT:USDT",
            "JUP/USDT:USDT",
            "KAIA/USDT:USDT",
            "KAITO/USDT:USDT",
            "KAS/USDT:USDT",
            "KAVA/USDT:USDT",
            "KDA/USDT:USDT",
            "KMNO/USDT:USDT",
            "KNC/USDT:USDT",
            "KOMA/USDT:USDT",
            "KSM/USDT:USDT",
            "LAYER/USDT:USDT",
            "LDO/USDT:USDT",
            "LEVER/USDT:USDT",
            "LINK/USDT:USDT",
            "LISTA/USDT:USDT",
            "LOKA/USDT:USDT",
            "LPT/USDT:USDT",
            "LQTY/USDT:USDT",
            "LRC/USDT:USDT",
            "LSK/USDT:USDT",
            "LTC/USDT:USDT",
            "LUMIA/USDT:USDT",
            "LUNA2/USDT:USDT",
            "MAGIC/USDT:USDT",
            "MANA/USDT:USDT",
            "MANTA/USDT:USDT",
            "MASK/USDT:USDT",
            "MAV/USDT:USDT",
            "MAVIA/USDT:USDT",
            "MBOX/USDT:USDT",
            "ME/USDT:USDT",
            "MELANIA/USDT:USDT",
            "MEME/USDT:USDT",
            "METIS/USDT:USDT",
            "MEW/USDT:USDT",
            "MINA/USDT:USDT",
            "MKR/USDT:USDT",
            "MLN/USDT:USDT",
            "MOCA/USDT:USDT",
            "MOODENG/USDT:USDT",
            "MORPHO/USDT:USDT",
            "MOVE/USDT:USDT",
            "MOVR/USDT:USDT",
            "MTL/USDT:USDT",
            "MUBARAK/USDT:USDT",
            "MYRO/USDT:USDT",
            "NEAR/USDT:USDT",
            "NEIRO/USDT:USDT",
            "NEIROETH/USDT:USDT",
            "NEO/USDT:USDT",
            "NFP/USDT:USDT",
            "NIL/USDT:USDT",
            "NKN/USDT:USDT",
            "NMR/USDT:USDT",
            "NOT/USDT:USDT",
            "NTRN/USDT:USDT",
            "NULS/USDT:USDT",
            "OGN/USDT:USDT",
            "OM/USDT:USDT",
            "OMNI/USDT:USDT",
            "ONDO/USDT:USDT",
            "ONE/USDT:USDT",
            "ONG/USDT:USDT",
            "ONT/USDT:USDT",
            "OP/USDT:USDT",
            "ORCA/USDT:USDT",
            "ORDI/USDT:USDT",
            "OXT/USDT:USDT",
            "PARTI/USDT:USDT",
            "PAXG/USDT:USDT",
            "PENDLE/USDT:USDT",
            "PENGU/USDT:USDT",
            "PEOPLE/USDT:USDT",
            "PERP/USDT:USDT",
            "PHA/USDT:USDT",
            "PHB/USDT:USDT",
            "PIPPIN/USDT:USDT",
            "PIXEL/USDT:USDT",
            "PLUME/USDT:USDT",
            "PNUT/USDT:USDT",
            "POL/USDT:USDT",
            "POLYX/USDT:USDT",
            "PONKE/USDT:USDT",
            "POPCAT/USDT:USDT",
            "PORTAL/USDT:USDT",
            "POWR/USDT:USDT",
            "PROM/USDT:USDT",
            "PYTH/USDT:USDT",
            "QNT/USDT:USDT",
            "QTUM/USDT:USDT",
            "QUICK/USDT:USDT",
            "RARE/USDT:USDT",
            "RAYSOL/USDT:USDT",
            "RDNT/USDT:USDT",
            "RED/USDT:USDT",
            "REI/USDT:USDT",
            "RENDER/USDT:USDT",
            "REZ/USDT:USDT",
            "RIF/USDT:USDT",
            "RLC/USDT:USDT",
            "RONIN/USDT:USDT",
            "ROSE/USDT:USDT",
            "RPL/USDT:USDT",
            "RSR/USDT:USDT",
            "RUNE/USDT:USDT",
            "RVN/USDT:USDT",
            "S/USDT:USDT",
            "SAFE/USDT:USDT",
            "SAGA/USDT:USDT",
            "SAND/USDT:USDT",
            "SANTOS/USDT:USDT",
            "SCR/USDT:USDT",
            "SCRT/USDT:USDT",
            "SEI/USDT:USDT",
            "SFP/USDT:USDT",
            "SHELL/USDT:USDT",
            "SIREN/USDT:USDT",
            "SKL/USDT:USDT",
            "SLERF/USDT:USDT",
            "SNX/USDT:USDT",
            "SOL/USDT:USDT",
            "SOLV/USDT:USDT",
            "SONIC/USDT:USDT",
            "SPELL/USDT:USDT",
            "SPX/USDT:USDT",
            "SSV/USDT:USDT",
            "STEEM/USDT:USDT",
            "STG/USDT:USDT",
            "STORJ/USDT:USDT",
            "STRK/USDT:USDT",
            "STX/USDT:USDT",
            "SUI/USDT:USDT",
            "SUN/USDT:USDT",
            "SUPER/USDT:USDT",
            "SUSHI/USDT:USDT",
            "SWARMS/USDT:USDT",
            "SWELL/USDT:USDT",
            "SXP/USDT:USDT",
            "SYN/USDT:USDT",
            "SYS/USDT:USDT",
            "T/USDT:USDT",
            "TAO/USDT:USDT",
            "THE/USDT:USDT",
            "THETA/USDT:USDT",
            "TIA/USDT:USDT",
            "TLM/USDT:USDT",
            "TNSR/USDT:USDT",
            "TOKEN/USDT:USDT",
            "TON/USDT:USDT",
            "TRB/USDT:USDT",
            "TROY/USDT:USDT",
            "TRU/USDT:USDT",
            "TRUMP/USDT:USDT",
            "TRX/USDT:USDT",
            "TST/USDT:USDT",
            "TURBO/USDT:USDT",
            "TUT/USDT:USDT",
            "TWT/USDT:USDT",
            "UMA/USDT:USDT",
            "UNI/USDT:USDT",
            "USTC/USDT:USDT",
            "USUAL/USDT:USDT",
            "UXLINK/USDT:USDT",
            "VANA/USDT:USDT",
            "VANRY/USDT:USDT",
            "VELODROME/USDT:USDT",
            "VET/USDT:USDT",
            "VIC/USDT:USDT",
            "VIDT/USDT:USDT",
            "VINE/USDT:USDT",
            "VIRTUAL/USDT:USDT",
            "VOXEL/USDT:USDT",
            "VTHO/USDT:USDT",
            "VVV/USDT:USDT",
            "W/USDT:USDT",
            "WAL/USDT:USDT",
            "WAXP/USDT:USDT",
            "WIF/USDT:USDT",
            "WLD/USDT:USDT",
            "WOO/USDT:USDT",
            "XAI/USDT:USDT",
            "XLM/USDT:USDT",
            "XMR/USDT:USDT",
            "XRP/USDT:USDT",
            "XTZ/USDT:USDT",
            "XVG/USDT:USDT",
            "XVS/USDT:USDT",
            "YFI/USDT:USDT",
            "YGG/USDT:USDT",
            "ZEC/USDT:USDT",
            "ZEN/USDT:USDT",
            "ZEREBRO/USDT:USDT",
            "ZETA/USDT:USDT",
            "ZIL/USDT:USDT",
            "ZK/USDT:USDT",
            "ZRO/USDT:USDT",
            "ZRX/USDT:USDT",
            "AERGO/USDT:USDT",
            "BANK/USDT:USDT",
            "FHE/USDT:USDT",
            "FORTH/USDT:USDT",
            "INIT/USDT:USDT",
            "KERNEL/USDT:USDT",
            "PROMPT/USDT:USDT",
            "PUMP/USDT:USDT",
            "STO/USDT:USDT",
            "WCT/USDT:USDT",
            "DEEP/USDT:USDT",
            "MEMEFI/USDT:USDT",
            "FIS/USDT:USDT",
            "DOOD/USDT:USDT",
            "JST/USDT:USDT",
            "PUNDIX/USDT:USDT",
            "CTK/USDT:USDT",
            "AIOT/USDT:USDT",
            "DOLO/USDT:USDT",
            "OBOL/USDT:USDT",
            "ASR/USDT:USDT",
            "ALPINE/USDT:USDT",
            "B2/USDT:USDT",
            "MILK/USDT:USDT",
            "XCN/USDT:USDT",
            "SYRUP/USDT:USDT",
            "SXT/USDT:USDT",
            "ZKJ/USDT:USDT",
            "CVC/USDT:USDT",
            "SKYAI/USDT:USDT",
            "NXPC/USDT:USDT",
            "AGT/USDT:USDT",
            "AWE/USDT:USDT",
            "B/USDT:USDT",
            "SOON/USDT:USDT",
            "SOPH/USDT:USDT",
            "HYPE/USDT:USDT",
            "BDXN/USDT:USDT",
            "HUMA/USDT:USDT",
            "MERL/USDT:USDT",
            "PUFFER/USDT:USDT",
            "RESOLV/USDT:USDT",
            "PORT3/USDT:USDT",
            "1000BOB/USDT:USDT",
            "LA/USDT:USDT",
            "SKAT/USDT:USDT",
            "HOME/USDT:USDT",
            "TAIKO/USDT:USDT",
            "SQD/USDT:USDT",
            "SPK/USDT:USDT",
            "DMC/USDT:USDT",
            "MYX/USDT:USDT",
            "F/USDT:USDT",
            "NEWT/USDT:USDT"

        ],
        "pair_blacklist": []
    },
    "pairlists": [
        {
            "method": "StaticPairList" /* 使用静态交易对列表 */
        }
    ],
    "api_server": {
        "enabled": true,
        "listen_ip_address": "0.0.0.0",
        "listen_port": 8080,
        "verbosity": "info",
        "enable_openapi": true,
        "jwt_secret_key": "7xL9#kQ2pZ4!vR8&wE5@tY6*uI1(oP3",
        "ws_token": "sdrfxvbcx34567",
        "CORS_origins": [
            "*"
        ],
        "username": "freqtrade",
        "password": "freqtrade123"
    },
    "bot_name": "freqtrade",
    "initial_state": "running",
    "force_entry_enable": false,
    "internals": {
        "process_throttle_secs": 3,
        "heartbeat_interval": 60,
        "sd_notify": true
    },
    "telegram": {
        "enabled": false,
        "token": "7861280994:AAFZqV1IGFFEb2oMVNvt70oFULmpn1S9rGU",
        "chat_id": "432493748"
    }
}